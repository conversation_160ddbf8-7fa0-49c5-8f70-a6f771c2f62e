<script setup lang="ts">
const { t } = useI18n()

useSeoMeta({
  title: `${t('pricing.title')} - Imagen`,
  description: t('pricing.description'),
  ogTitle: `${t('pricing.title')} - Imagen`,
  ogDescription: t('pricing.description')
})

const plans = ref([
  {
    title: t('Imagen'),
    description: t('Create images from text prompts.'),
    price: t('8 '),
    billingCycle: '/Image',
    billingPeriod: 'Credits',
    features: [
      t('Gemini 2.0 Flash'),
      t('Imagen 4 Fast'),
      t('Imagen 4'),
      t('Imagen 4 Ultra'),
      t('16+ Styles'),
      t('Image Reference')
    ],
    button: {
      label: 'Buy credits',
      variant: 'soft' as const
    }
  },
  {
    title: t('Video Gen'),
    scale: true,
    description: t('Generate videos from text prompts and images.'),
    price: t('15 '),
    billingCycle: '/Video',
    billingPeriod: 'Credits',
    features: [
      t('Veo 2'),
      t('Veo 3'),
      t('Text to Video'),
      t('Image to Video'),
      t('Up to 8 seconds'),
      t('1080p Quality'),
      t('Multiple Styles')
    ],
    button: {
      label: 'Buy credits',
      variant: 'soft' as const
    }
  },
  {
    title: t('Speech Gen'),
    description: t('Convert text and documents to natural speech.'),
    price: t('5 '),
    billingCycle: '/1000 chars',
    billingPeriod: 'Credits',
    features: [
      t('Text to Speech'),
      t('Document to Speech'),
      t('Multi-Speaker Support'),
      t('400+ Voices'),
      t('Multiple Languages'),
      t('Emotion Control')
    ],
    button: {
      label: 'Buy credits',
      variant: 'soft' as const
    }
  },
  {
    title: t('Dialogue Gen'),
    description: t('Create natural conversations with multiple speakers.'),
    price: t('10 '),
    billingCycle: '/Dialogue',
    billingPeriod: 'Credits',
    features: [
      t('Multi-Speaker Dialogue'),
      t('Natural Conversations'),
      t('Voice Customization'),
      t('Emotion Expression'),
      t('Script Generation'),
      t('Audio Export')
    ],
    button: {
      label: 'Buy credits',
      variant: 'soft' as const
    }
  }
])
</script>

<template>
  <UPage>
    <UPageSection>
      <UPricingPlans
        compact
        :plans="plans"
      />
    </UPageSection>
  </UPage>
</template>
